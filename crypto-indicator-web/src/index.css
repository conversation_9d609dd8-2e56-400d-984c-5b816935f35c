/* CSS Custom Properties for Comfortable Dark Theme */
:root {
  /* Comfortable Dark Base Colors (inspired by Catppuccin/Night Owl) */
  --bg-primary: #1e1e2e;
  --bg-secondary: #181825;
  --bg-surface: #313244;
  --bg-elevated: #45475a;

  /* Subtle Glass Effects */
  --glass-bg: rgb(255 255 255 / 0.05);
  --glass-bg-strong: rgb(255 255 255 / 0.08);
  --glass-border: rgb(255 255 255 / 0.15);
  --glass-shadow: 0 4px 16px rgb(0 0 0 / 0.4);
  --glass-blur: blur(12px);
  --glass-blur-strong: blur(16px);

  /* Comfortable Background Gradients */
  --dark-bg-primary: var(--bg-primary);
  --dark-bg-secondary: var(--bg-secondary);
  --dark-bg-gradient: linear-gradient(135deg, #1e1e2e 0%, #181825 50%, #313244 100%);

  /* Comfortable Text Colors */
  --text-primary: #cdd6f4;
  --text-secondary: #bac2de;
  --text-muted: #9399b2;

  /* Soft Accent Colors (muted and comfortable) */
  --accent-blue: #89b4fa;
  --accent-green: #a6e3a1;
  --accent-yellow: #f9e2af;
  --accent-red: #f38ba8;
  --accent-purple: #cba6f7;
  --accent-teal: #94e2d5;

  /* Legacy color mappings for compatibility */
  --neon-cyan: var(--accent-teal);
  --neon-blue: var(--accent-blue);
  --neon-purple: var(--accent-purple);
  --neon-pink: var(--accent-red);
  --neon-green: var(--accent-green);
  --neon-yellow: var(--accent-yellow);
  --accent-cyan: var(--accent-teal);

  /* Subtle Border and Interaction Colors */
  --border-primary: #45475a;
  --border-hover: #585b70;
  --border-active: #6c7086;

  /* Removed aggressive glow effects - using subtle shadows instead */
  --shadow-subtle: 0 2px 8px rgb(0 0 0 / 0.3);
  --shadow-hover: 0 4px 12px rgb(0 0 0 / 0.4);

  /* RGB color values for glass effects */
  --bg-primary-rgb: 30, 30, 46;
  --bg-secondary-rgb: 49, 50, 68;
  --bg-tertiary-rgb: 88, 91, 112;
  --accent-blue-rgb: 137, 180, 250;
  --accent-purple-rgb: 203, 166, 247;
  --accent-red-rgb: 243, 139, 168;
  --accent-green-rgb: 166, 227, 161;

  /* Glass Effect Variables */
  --glass-blur-primary: blur(20px);
  --glass-blur-secondary: blur(12px);
  --glass-blur-tertiary: blur(8px);
  --glass-opacity-primary: 0.85;
  --glass-opacity-secondary: 0.75;
  --glass-opacity-tertiary: 0.65;
  --glass-border-primary: 1px solid rgb(255 255 255 / 0.2);
  --glass-border-secondary: 1px solid rgb(255 255 255 / 0.15);
  --glass-border-tertiary: 1px solid rgb(255 255 255 / 0.1);
  --glass-shadow-primary: 0 8px 32px rgb(0 0 0 / 0.12), 0 2px 8px rgb(0 0 0 / 0.08);
  --glass-shadow-secondary: 0 4px 16px rgb(0 0 0 / 0.1), 0 1px 4px rgb(0 0 0 / 0.06);
  --glass-shadow-tertiary: 0 2px 8px rgb(0 0 0 / 0.08), 0 1px 2px rgb(0 0 0 / 0.04);

  /* Spring Animation Variables */
  --spring-duration: 0.4s;
  --spring-easing: cubic-bezier(0.34, 1.56, 0.64, 1);
  --micro-spring: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
  height: 100vh;
  margin: 0;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--text-primary);
  background: var(--bg-primary);
}

.app-container {
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  padding: 20px;
  overflow: hidden auto;
  background: var(--dark-bg-gradient);
}

.app-container::before {
  position: fixed;
  inset: 0;
  z-index: -1;
  pointer-events: none;
  content: '';
  background:
    radial-gradient(circle at 20% 80%, rgb(148 226 213 / 0.03) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgb(203 166 247 / 0.02) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgb(137 180 250 / 0.02) 0%, transparent 60%);
}

.header {
  position: relative;
  padding: 20px 24px;
  margin-bottom: 30px;
  overflow: hidden;
  background: var(--glass-bg);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.header:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-hover);
}

.header::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  gap: 12px;
  align-items: center;
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.lightning-icon {
  font-size: 2.4rem;
  color: var(--accent-yellow);
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 0.3));
  transition: all 0.3s ease;
}

.lightning-icon:hover {
  filter: drop-shadow(0 2px 6px rgb(0 0 0 / 0.4));
  transform: scale(1.05);
}

.title-text {
  text-shadow: 0 2px 8px rgb(0 0 0 / 0.4);
}

.header-status {
  display: flex;
  align-items: center;
}

.live-indicator {
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--accent-green);
  background: rgb(166 227 161 / 0.1);
  border: 1px solid var(--accent-green);
  border-radius: 20px;
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-green);
  border-radius: 50%;
  animation: live-pulse 2s ease-in-out infinite;
}

@keyframes live-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.live-text {
  letter-spacing: 0.5px;
}

.header-subtitle {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  opacity: 0.9;
}

.table-container {
  position: relative;
  overflow: auto visible;
  background: var(--glass-bg-strong);
  border: 1px solid var(--border-primary);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  -webkit-overflow-scrolling: touch;
}

.table-container:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-hover);
}

.table-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-purple), transparent);
  opacity: 0.3;
}

.stats-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  color: var(--text-primary);
  background: rgb(148 226 213 / 0.03);
  border-bottom: 1px solid var(--border-primary);
}

.stats-info button {
  padding: 10px 20px;
  margin-left: 15px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.stats-info button:hover:not(:disabled) {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: translateY(-1px);
}

.stats-info button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.table {
  position: relative;
  width: 100%;
  min-width: 700px;
  font-size: 14px;
  table-layout: auto;
  border-collapse: collapse;
  background: transparent;
}

/* Sticky first column for all screen sizes */
.table th:first-child,
.table td:first-child {
  position: sticky;
  left: 0;
  z-index: 10;
  min-width: 120px;
  background: var(--bg-primary);
  border-right: 2px solid var(--border-primary);
  box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
}

.table th:first-child {
  z-index: 11;
  font-weight: 600;
  background: var(--bg-surface);
}

/* Ensure sticky column maintains row hover effects with solid backgrounds */
.table tr:hover td:first-child {
  background: var(--bg-elevated);
}

/* Clickable Symbol Styles */
.clickable-symbol {
  position: relative;
  display: inline-block;
  padding: 6px 10px;
  font-weight: 600;
  text-decoration: none;
  text-decoration: underline;
  text-decoration-color: transparent;
  text-underline-offset: 3px;
  cursor: pointer;
  background: rgb(137 180 250 / 0.05);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.clickable-symbol::before {
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 10px;
  content: '🔗';
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.clickable-symbol::after {
  position: absolute;
  bottom: 2px;
  left: 50%;
  width: 0;
  height: 1px;
  content: '';
  background: var(--accent-blue);
  transform: translateX(-50%);
  transition: all 0.2s ease;
}

.clickable-symbol:hover {
  color: var(--accent-blue);
  text-decoration-color: var(--accent-blue);
  background: rgb(137 180 250 / 0.15);
  border-color: var(--accent-blue);
  box-shadow: 0 4px 12px rgb(137 180 250 / 0.3);
  transform: translateY(-2px);
}

.clickable-symbol:hover::before {
  opacity: 1;
}

.clickable-symbol:hover::after {
  width: 80%;
}

.clickable-symbol:active {
  box-shadow: 0 2px 6px rgb(137 180 250 / 0.4);
  transform: translateY(-1px);
}

/* Sortable Table Header Styles */
.sortable-header {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable-header:hover {
  background: var(--bg-elevated);
}

.sortable-header.active {
  background: var(--bg-secondary);
}

.sortable-header-content {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-text {
  flex: 1;
}

.sort-icon {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.sortable-header:hover .sort-icon,
.sortable-header.active .sort-icon {
  color: var(--accent-blue);
  opacity: 1;
}

/* Table Filters Styles */
.table-filters {
  padding: 16px;
  margin: 16px 0;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
  margin-bottom: 12px;
}

.filters-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-input,
.filter-select {
  padding: 6px 8px;
  font-size: 13px;
  color: var(--text-primary);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgb(137 180 250 / 0.2);
}

.range-group {
  min-width: 200px;
}

.range-inputs {
  display: flex;
  gap: 8px;
  align-items: center;
}

.range-input {
  flex: 1;
  min-width: 80px;
}

.range-separator {
  font-weight: 500;
  color: var(--text-secondary);
}

.filter-actions {
  display: flex;
  align-items: flex-end;
  margin-left: auto;
}

.clear-filters-btn {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  background: var(--accent-red);
  border: none;
  border-radius: 4px;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.clear-filters-btn:hover {
  background: #e74c3c;
  transform: translateY(-1px);
}

.filter-results {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  border-top: 1px solid var(--border-primary);
}

.results-count {
  font-weight: 500;
}

.filtered-indicator {
  font-weight: 600;
  color: var(--accent-blue);
}

/* Mobile Responsive Styles for Filters */
@media (width <= 768px) {
  .table-filters {
    padding: 12px;
  }

  .filters-row {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-group {
    width: 100%;
    min-width: auto;
  }

  .range-group {
    min-width: auto;
  }

  .range-inputs {
    flex-direction: column;
    gap: 8px;
  }

  .range-input {
    min-width: auto;
  }

  .filter-actions {
    align-items: stretch;
    margin-left: 0;
  }

  .clear-filters-btn {
    width: 100%;
    padding: 8px 12px;
  }

  .filter-results {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }

  /* Sortable headers mobile adjustments */
  .sortable-header-content {
    gap: 4px;
  }

  .sort-icon {
    font-size: 10px;
  }
}

/* Apple Liquid Glass Design System */

/* Enhanced Glass Effects */
.glass-primary {
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
}

.glass-secondary {
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
}

.glass-tertiary {
  background: rgb(var(--bg-tertiary-rgb), var(--glass-opacity-tertiary));
  border: var(--glass-border-tertiary);
  box-shadow: var(--glass-shadow-tertiary);
  backdrop-filter: var(--glass-blur-tertiary);
}

/* Responsive Layout Containers */
.responsive-table-container {
  width: 100%;
  transition: all var(--spring-duration) var(--spring-easing);
}

/* Mobile Layout Styles */
.mobile-layout {
  position: relative;
  padding: 16px;
}

.crypto-cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 60px; /* Space for filter toggle */
}

.crypto-card {
  position: relative;
  padding: 16px;
  overflow: hidden;
  cursor: pointer;
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
  transition: all var(--micro-spring);
}

.crypto-card::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 2px;
  content: '';
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  opacity: 0;
  transition: opacity var(--micro-spring);
}

.crypto-card:hover::before,
.crypto-card:focus::before {
  opacity: 1;
}

.crypto-card:hover {
  box-shadow: var(--glass-shadow-primary);
  transform: translateY(-2px) scale(1.02);
}

.crypto-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.crypto-card-symbol {
  font-size: 18px;
  font-weight: 600;
}

.crypto-card-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-end;
}

.price-label {
  font-size: 10px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent-green);
}

.crypto-card-signals {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.signal-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.signal-label-card {
  font-size: 10px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.crypto-card-details {
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid var(--border-primary);
  animation: expand-details var(--micro-spring) ease-out;
}

@keyframes expand-details {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.crypto-card-expand-indicator {
  position: absolute;
  right: 12px;
  bottom: 8px;
}

.expand-icon {
  font-size: 10px;
  color: var(--text-secondary);
  transition: transform var(--micro-spring);
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Filter Toggle (Mobile FAB) */
.filter-toggle-container {
  position: fixed;
  top: 24px;
  left: 50%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  transform: translateX(-50%);
}

.filter-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  cursor: pointer;
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 50%;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
  transition: all var(--micro-spring);
}

.filter-toggle:hover {
  box-shadow: var(--glass-shadow-primary);
  transform: scale(1.1);
}

.filter-toggle.active {
  background: rgb(var(--accent-blue-rgb), 0.9);
}

.filter-icon {
  font-size: 20px;
}

.filter-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  background: var(--accent-red);
  border-radius: 50%;
}

.filter-status {
  padding: 4px 8px;
  font-size: 10px;
  color: var(--text-secondary);
  white-space: nowrap;
  background: rgb(var(--bg-tertiary-rgb), var(--glass-opacity-tertiary));
  border: var(--glass-border-tertiary);
  border-radius: 12px;
  box-shadow: var(--glass-shadow-tertiary);
  backdrop-filter: var(--glass-blur-tertiary);
}

/* Filter Drawer (Mobile Top Sheet) */
.filter-drawer-backdrop {
  position: fixed;
  inset: 0;
  z-index: 1100;
  background: rgb(0 0 0 / 0.5);
  backdrop-filter: blur(4px);
  animation: fade-in var(--micro-spring) ease-out;
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.filter-drawer {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1200;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 40vh;
  max-height: 85vh;
  overflow: hidden;
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 0 0 24px 24px;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
  animation: slide-down var(--spring-duration) var(--spring-easing);
}

@keyframes slide-down {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

.filter-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-primary);
}

.filter-drawer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.filter-drawer-close {
  padding: 4px;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 50%;
  transition: all var(--micro-spring);
}

.filter-drawer-close:hover {
  color: var(--text-primary);
  background: var(--bg-elevated);
}

.filter-drawer-content {
  flex: 1;
  min-height: 0;
  padding: 16px 24px;
  overflow: hidden auto;
  scrollbar-width: thin;
  -webkit-overflow-scrolling: touch;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.filter-drawer-input,
.filter-drawer-select {
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  color: var(--text-primary);
  background: var(--bg-elevated);
  border: var(--glass-border-secondary);
  border-radius: 12px;
  transition: all var(--micro-spring);
}

.filter-drawer-input:focus,
.filter-drawer-select:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgb(var(--accent-blue-rgb), 0.2);
}

.range-inputs-mobile {
  display: flex;
  gap: 12px;
}

.range-inputs-mobile .filter-drawer-input {
  flex: 1;
}

.filter-drawer-footer {
  padding: 16px 24px 24px;
  border-top: 1px solid var(--border-primary);
}

.filter-results-mobile {
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.filter-drawer-actions {
  display: flex;
  gap: 12px;
}

.filter-drawer-clear,
.filter-drawer-apply {
  flex: 1;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  border-radius: 12px;
  transition: all var(--micro-spring);
}

.filter-drawer-clear {
  color: var(--text-secondary);
  background: var(--bg-elevated);
}

.filter-drawer-clear:hover {
  color: white;
  background: var(--accent-red);
}

.filter-drawer-apply {
  color: white;
  background: var(--accent-blue);
}

.filter-drawer-apply:hover {
  background: var(--accent-purple);
  transform: translateY(-1px);
}

/* Mobile-specific FilterDrawer improvements */
@media (width <= 768px) {
  .filter-drawer {
    min-height: 50vh;
    max-height: 90vh;
  }

  .filter-drawer-content {
    max-height: calc(90vh - 140px); /* Account for header and footer */
    padding: 12px 20px;
  }

  .filter-section {
    margin-bottom: 16px;
  }

  .filter-drawer-header {
    flex-shrink: 0;
    padding: 16px 20px 12px;
  }

  .filter-drawer-footer {
    flex-shrink: 0;
    padding: 12px 20px 20px;
  }
}

@media (width <= 480px) {
  .filter-drawer {
    min-height: 60vh;
    max-height: 95vh;
  }

  .filter-drawer-content {
    max-height: calc(95vh - 120px);
    padding: 8px 16px;
  }

  .filter-drawer-header {
    padding: 12px 16px 8px;
  }

  .filter-drawer-footer {
    padding: 8px 16px 16px;
  }
}

/* Tablet Layout Styles */
.tablet-layout {
  padding: 16px;
}

.crypto-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.tablet-layout .crypto-card {
  min-height: 140px;
}

/* Desktop Layout Enhancements */
.desktop-layout .table-wrapper {
  margin-top: 16px;
  overflow: hidden;
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
}

.desktop-layout .table {
  background: transparent;
}

/* Enhanced Table Filters for Desktop */
.desktop-layout .table-filters {
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
}



/* Responsive Breakpoint Adjustments */
@media (width <= 767px) {
  .layout-mobile .table-filters,
  .layout-mobile .sortable-header {
    display: none;
  }
}

@media (width >= 768px) and (width <= 1023px) {
  .layout-tablet .filter-toggle-container {
    display: none;
  }

  .layout-tablet .table-filters {
    background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
    border: var(--glass-border-secondary);
    border-radius: 12px;
    box-shadow: var(--glass-shadow-secondary);
    backdrop-filter: var(--glass-blur-secondary);
  }
}

@media (width >= 1024px) {
  .layout-desktop .filter-toggle-container,
  .layout-desktop .crypto-cards-container,
  .layout-desktop .crypto-cards-grid {
    display: none;
  }
}

/* Spring Animation Classes */
.spring-enter {
  animation: spring-enter var(--spring-duration) var(--spring-easing);
}

@keyframes spring-enter {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.spring-exit {
  animation: spring-exit var(--micro-spring) ease-in;
}

@keyframes spring-exit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  to {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
}

/* Enhanced Hover Effects */
.interactive-element {
  cursor: pointer;
  transition: all var(--micro-spring);
}

.interactive-element:hover {
  box-shadow: var(--glass-shadow-secondary);
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.table tr:nth-child(even) td:first-child {
  background: var(--bg-secondary);
}

.table th {
  position: relative;
  padding: 15px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgb(255 255 255 / 0.1);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur);
}

.table th::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.4;
}

.table td {
  padding: 15px;
  color: var(--text-primary);
  text-align: center;
  background: transparent;
  border-bottom: 1px solid var(--border-primary);
}

.table tr:hover {
  background: rgb(148 226 213 / 0.05);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.table tr:nth-child(even) {
  background: rgb(255 255 255 / 0.01);
}

.rank-badge {
  display: inline-block;
  min-width: 30px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 700;
  color: var(--accent-teal);
  background: var(--glass-bg-strong);
  border: 1px solid var(--accent-teal);
  border-radius: 12px;
  box-shadow: var(--shadow-subtle);
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.rank-badge:hover {
  box-shadow: var(--shadow-hover);
  transform: scale(1.05);
}

.crypto-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-right: 10px;
  font-size: 12px;
  font-weight: 700;
  color: white;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border: 1px solid var(--border-primary);
  border-radius: 50%;
  box-shadow: var(--shadow-subtle);
  transition: all 0.3s ease;
}

.crypto-icon:hover {
  box-shadow: var(--shadow-hover);
  transform: scale(1.05);
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--text-primary);
}

.signal-badge {
  position: relative;
  box-sizing: border-box;
  display: inline-flex;
  gap: 6px;
  align-items: center;
  min-height: 32px;
  padding: 8px 16px;
  overflow: visible;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  text-shadow: 0 0 8px currentcolor;
  border: 1px solid transparent;
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

/* Custom fast tooltip for signal badges */
.signal-badge-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  z-index: 1000;
  padding: 8px 12px;
  margin-bottom: 8px;
  font-size: 11px;
  font-weight: 400;
  color: var(--text-primary);
  text-transform: none;
  white-space: nowrap;
  pointer-events: none;
  background: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-hover);
  opacity: 0;
  transform: translateX(-50%);
  transform: translateX(-50%) translateY(5px);
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
}

.signal-badge-tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.signal-badge-tooltip::after {
  position: absolute;
  top: 100%;
  left: 50%;
  content: '';
  border: 5px solid transparent;
  border-top-color: var(--border-primary);
  transform: translateX(-50%);
}

.signal-badge::before {
  position: absolute;
  inset: 0;
  z-index: -1;
  content: '';
  background: inherit;
  opacity: 0.1;
}

/* Signal Badge States */
.signal-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  font-size: 14px;
  font-weight: 700;
}

.signal-label {
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signal-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  font-size: 14px;
  animation: spin 1s linear infinite;
}

/* Interactive States */
.clickable-signal {
  cursor: pointer;
  border-width: 2px;
  box-shadow: var(--shadow-subtle);
  transition: all 0.3s ease;
}

.clickable-signal:hover {
  border-color: currentcolor;
  box-shadow: var(--shadow-hover);
  backdrop-filter: var(--glass-blur-strong);
  transform: translateY(-2px) scale(1.05);
}

.clickable-signal:focus {
  outline: none;
  box-shadow: var(--shadow-hover), 0 0 0 3px rgb(255 255 255 / 0.2);
  transform: translateY(-1px);
}

.clickable-signal:active {
  box-shadow: var(--shadow-subtle);
  transform: translateY(0) scale(1.02);
}

/* Loading and Disabled States */
.signal-loading {
  pointer-events: none;
  cursor: wait;
  opacity: 0.7;
}

.signal-disabled {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
  filter: grayscale(0.3);
}

/* Signal Color Variants - Semantic Trading Colors */
.signal-gold {
  color: var(--accent-yellow);
  background: rgb(249 226 175 / 0.2);
  border-color: rgb(249 226 175 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(249 226 175 / 0.3);
}

.signal-gold.clickable-signal {
  background: rgb(249 226 175 / 0.25);
  border-color: var(--accent-yellow);
}

.signal-gold.clickable-signal:hover {
  background: rgb(249 226 175 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(249 226 175 / 0.5);
}

.signal-blue {
  color: var(--accent-blue);
  background: rgb(137 180 250 / 0.2);
  border-color: rgb(137 180 250 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(137 180 250 / 0.3);
}

.signal-blue.clickable-signal {
  background: rgb(137 180 250 / 0.25);
  border-color: var(--accent-blue);
}

.signal-blue.clickable-signal:hover {
  background: rgb(137 180 250 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(137 180 250 / 0.5);
}

.signal-gray {
  color: var(--text-muted);
  background: rgb(147 153 178 / 0.2);
  border-color: rgb(147 153 178 / 0.4);
  box-shadow: var(--shadow-subtle), 0 0 12px rgb(147 153 178 / 0.2);
}

.signal-gray.clickable-signal {
  background: rgb(147 153 178 / 0.25);
  border-color: var(--text-muted);
}

.signal-gray.clickable-signal:hover {
  background: rgb(147 153 178 / 0.35);
  box-shadow: var(--shadow-hover), 0 0 20px rgb(147 153 178 / 0.4);
}

.signal-default {
  color: var(--text-muted);
  background: rgb(147 153 178 / 0.15);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-subtle);
}

.loading-container {
  max-width: 600px;
  padding: 60px 40px;
  margin: 40px auto;
  color: var(--text-primary);
  text-align: center;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
}

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: rgb(0 0 0 / 0.95);
  backdrop-filter: var(--glass-blur);
  animation: modal-fade-in 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }

  to {
    opacity: 1;
    backdrop-filter: var(--glass-blur);
  }
}

.chart-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 95vw;
  max-width: 95vw;
  height: 75vh;
  max-height: 75vh;
  padding: 24px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur-strong);
  animation: modal-slide-in 0.3s ease-out;
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.chart-modal-content::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--glass-border);
}

.chart-header::after {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgb(0 0 0 / 0.3);
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  padding: 0;
  font-size: 20px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: scale(1.1);
}

/* Mobile-specific close button improvements */
@media (width <= 768px) {
  .close-button {
    /* Ensure it's always accessible */
    z-index: 1001;
    width: 48px;
    height: 48px;
    padding: 12px;
    font-size: 24px;
  }
}

@media (width <= 480px) {
  .close-button {
    width: 52px;
    height: 52px;
    padding: 14px;
    font-size: 28px;
  }
}

.chart-container {
  position: relative;
  flex: 1;
  width: 100%;
  min-width: 600px;
  min-height: 400px;
  margin: 20px 0;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
}

.chart-container::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  justify-content: center;
  padding: 16px;
  margin-top: 20px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  backdrop-filter: var(--glass-blur);
}

.legend-item {
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 0.3);
}

/* Mobile Breakpoints - Progressive Enhancement */
@media (width <= 480px) {
  .app-container {
    padding: 10px;
  }

  .header {
    padding: 12px 16px;
    margin-bottom: 20px;
  }

  .header-main {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .header-title {
    gap: 8px;
    font-size: 1.6rem;
  }

  .lightning-icon {
    font-size: 1.8rem;
  }

  .header-subtitle {
    font-size: 0.85rem;
  }

  .live-indicator {
    padding: 4px 8px;
    font-size: 0.65rem;
  }

  .live-dot {
    width: 4px;
    height: 4px;
  }

  /* Table Container - Enable horizontal scrolling with sticky first column */
  .table-container {
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Table - Minimum width to prevent cramping */
  .table {
    position: relative;
    min-width: 600px;
    table-layout: auto;
  }

  /* Sticky first column for mobile - solid backgrounds */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
  }

  .table th:first-child {
    z-index: 11;
    background: var(--bg-surface);
  }

  /* Mobile sticky column hover and alternating row backgrounds */
  .table tr:hover td:first-child {
    background: var(--bg-elevated);
  }

  .table tr:nth-child(even) td:first-child {
    background: var(--bg-secondary);
  }

  .table th, .table td {
    padding: 16px 12px;
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
  }

  .table th {
    font-size: 13px;
    font-weight: 600;
  }

  /* Signal Badge - WCAG AAA compliant touch targets */
  .signal-badge {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    min-height: 48px;
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 12px;
  }

  .signal-icon {
    min-width: 16px;
    font-size: 16px;
  }

  .signal-label-mobile {
    font-size: 12px;
    font-weight: 500;
  }

  /* Crypto Icon - Larger for better visibility */
  .crypto-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* Chart Modal - Full screen on small devices */
  .chart-modal-content {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    height: calc(100vh - 10px);
    max-height: calc(100vh - 10px);
    padding: 12px;
    margin: 5px;
    border-radius: 16px;
  }

  .chart-container {
    min-width: 280px;
    min-height: 200px;
    margin: 10px 0;
  }

  .chart-legend {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
    margin-top: 10px;
  }

  .legend-item {
    padding: 4px 8px;
    font-size: 12px;
  }

  /* Close button - Larger touch target */
  .chart-close-button {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    font-size: 18px;
  }
}

@media (width <= 768px) and (width >= 481px) {
  .app-container {
    padding: 15px;
  }

  .header {
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  .header-main {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-title {
    gap: 10px;
    font-size: 1.8rem;
  }

  .lightning-icon {
    font-size: 2rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .live-indicator {
    padding: 4px 12px;
    font-size: 0.75rem;
  }

  .live-dot {
    width: 6px;
    height: 6px;
  }

  .stats-info {
    flex-direction: column;
    gap: 15px;
    padding: 16px;
    text-align: center;
  }

  /* Table - Better readability with sticky first column */
  .table-container {
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table {
    position: relative;
    min-width: 700px;
  }

  /* Sticky first column for tablets - solid backgrounds */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
  }

  .table th:first-child {
    z-index: 11;
    background: var(--bg-surface);
  }

  /* Tablet sticky column hover and alternating row backgrounds */
  .table tr:hover td:first-child {
    background: var(--bg-elevated);
  }

  .table tr:nth-child(even) td:first-child {
    background: var(--bg-secondary);
  }

  .table th, .table td {
    padding: 14px 10px;
    font-size: 13px;
    line-height: 1.3;
  }

  /* Signal Badge - Improved touch targets */
  .signal-badge {
    gap: 6px;
    min-width: 44px;
    min-height: 44px;
    padding: 10px 14px;
    font-size: 12px;
    border-radius: 10px;
  }

  .signal-icon {
    min-width: 14px;
    font-size: 14px;
  }

  .signal-label {
    font-size: 11px;
  }

  .crypto-icon {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .chart-modal-content {
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    height: calc(85vh - 20px);
    max-height: calc(85vh - 20px);
    padding: 16px;
    margin: 10px;
    border-radius: 20px;
  }

  .chart-container {
    min-width: 320px;
    min-height: 240px;
    margin: 15px 0;
  }

  .chart-legend {
    gap: 10px;
    padding: 10px;
    margin-top: 12px;
  }

  .legend-item {
    font-size: 12px;
  }
}

/* Landscape Orientation Support */
@media (height <= 500px) and (orientation: landscape) {
  .app-container {
    padding: 8px;
  }

  .header {
    padding: 8px 16px;
    margin-bottom: 16px;
  }

  .header-main {
    flex-direction: row;
    gap: 16px;
    align-items: center;
  }

  .header-title {
    font-size: 1.4rem;
  }

  .chart-modal-content {
    height: calc(100vh - 10px);
    max-height: calc(100vh - 10px);
    padding: 8px;
  }

  .chart-container {
    min-height: 180px;
    margin: 8px 0;
  }

  .chart-legend {
    padding: 6px;
    margin-top: 8px;
  }
}

/* Touch-friendly improvements */
@media (pointer: coarse) {
  /* Increase touch targets for all interactive elements */
  button, .signal-badge, .chart-close-button {
    min-width: 48px;
    min-height: 48px;
  }

  /* Better spacing for touch interaction */
  .table th, .table td {
    padding: 16px 12px;
  }

  /* Larger text for better readability */
  .table {
    font-size: 14px;
  }
}

/* High DPI displays */
@media (resolution >= 2dppx), (resolution >= 192dpi) {
  .signal-badge, .crypto-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Very small screens - Alternative card layout */
@media (width <= 360px) {
  .table-container {
    overflow-x: visible;
  }

  .table {
    display: block;
    min-width: auto;
  }

  .table thead {
    display: none;
  }

  .table tbody {
    display: block;
  }

  .table tr {
    display: block;
    padding: 16px;
    margin-bottom: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
  }

  .table td {
    display: block;
    padding: 8px 0;
    font-size: 14px;
    border: none;
  }

  .table td::before {
    display: inline-block;
    width: 120px;
    font-weight: 600;
    color: var(--text-secondary);
    content: attr(data-label) ": ";
  }

  .signal-badge {
    margin-top: 8px;
  }
}

/* Loading State Styles */
.loading-state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
  border: 3px solid rgb(255 255 255 / 0.1);
  border-top: 3px solid var(--accent-cyan);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgb(0 0 0 / 0.3);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
}

.loading-small .loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .loading-spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

/* Error State Styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  margin-bottom: 15px;
  font-size: 48px;
  color: var(--accent-red, #ef4444);
}

.error-message {
  max-width: 400px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: #ef4444;
}

.retry-button {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgb(255 255 255 / 0.2);
  border-color: rgb(255 255 255 / 0.3);
  box-shadow: 0 4px 16px rgb(0 0 0 / 0.2);
  transform: translateY(-1px);
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  margin: 20px;
  text-align: center;
  background: var(--glass-bg-strong);
  border: 1px solid var(--neon-pink);
  border-radius: 16px;
  box-shadow: var(--glass-shadow), 0 0 20px rgb(243 139 168 / 0.3);
  backdrop-filter: var(--glass-blur);
}

.error-boundary h3 {
  margin: 0 0 10px;
  font-weight: 600;
  color: var(--neon-pink);
  text-shadow: 0 0 10px rgb(255 0 255 / 0.5);
}

.error-boundary p {
  margin: 0 0 20px;
  color: var(--text-secondary);
}

/* AMOLED Specific Enhancements */
.table th {
  color: var(--text-primary);
  text-shadow: 0 0 8px rgb(255 255 255 / 0.3);
  background: rgb(0 255 255 / 0.05);
  border-bottom: 1px solid var(--neon-cyan);
}

.table td {
  color: var(--text-primary);
  border-bottom: 1px solid rgb(255 255 255 / 0.1);
}

/* Comfortable Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-teal);
  border-radius: 4px;
  box-shadow: var(--shadow-subtle);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
  box-shadow: var(--shadow-hover);
}

/* Selection styling */
::selection {
  color: var(--text-primary);
  background: rgb(148 226 213 / 0.3);
}

/* DarkReader Controls Styling */
.darkreader-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  padding: 12px;
  font-size: 12px;
  color: var(--text-primary);
  background: var(--glass-bg-strong);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--glass-shadow);
  backdrop-filter: var(--glass-blur);
}

.darkreader-main-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.darkreader-toggle {
  padding: 6px 12px;
  font-size: 11px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.darkreader-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-teal);
}

.darkreader-toggle.enabled {
  color: var(--accent-teal);
  background: rgb(148 226 213 / 0.2);
  border-color: var(--accent-teal);
}

.darkreader-settings-toggle {
  padding: 6px 8px;
  font-size: 11px;
  color: var(--text-primary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.darkreader-settings-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-blue);
}

.darkreader-advanced-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid var(--glass-border);
}

.darkreader-control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.darkreader-control-group label {
  font-size: 10px;
  font-weight: 500;
  color: var(--text-secondary);
}

.darkreader-slider {
  width: 100%;
  height: 4px;
  appearance: none;
  outline: none;
  background: var(--bg-surface);
  border-radius: 2px;
}

.darkreader-slider::-webkit-slider-thumb {
  width: 12px;
  height: 12px;
  appearance: none;
  cursor: pointer;
  background: var(--accent-teal);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgb(0 0 0 / 0.3);
}

.darkreader-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  cursor: pointer;
  background: var(--accent-teal);
  border: none;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgb(0 0 0 / 0.3);
}

.darkreader-presets {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.darkreader-preset {
  flex: 1;
  min-width: 0;
  padding: 4px 8px;
  font-size: 9px;
  color: var(--text-secondary);
  cursor: pointer;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.darkreader-preset:hover {
  color: var(--accent-purple);
  background: var(--glass-bg-strong);
  border-color: var(--accent-purple);
}

/* Mobile DarkReader controls optimization */
@media (width <= 768px) {
  .darkreader-controls {
    top: 10px;
    right: 10px;
    padding: 8px;
    font-size: 11px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    min-height: 44px;
    padding: 8px 12px;
    font-size: 12px;
  }

  .darkreader-advanced-controls {
    min-width: 160px;
  }

  .darkreader-slider::-webkit-slider-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-preset {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 10px;
  }
}

@media (width <= 480px) {
  .darkreader-controls {
    top: 5px;
    right: 5px;
    padding: 6px;
    font-size: 10px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    min-width: 48px;
    min-height: 48px;
    padding: 10px 14px;
    font-size: 11px;
  }

  .darkreader-advanced-controls {
    min-width: 140px;
  }
}
