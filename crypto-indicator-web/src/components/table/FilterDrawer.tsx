import React, { useEffect } from "react";

import { useTouchGestures } from "../../hooks/useTouchGestures";

import { FilterDrawerContent } from "./FilterDrawerContent";
import { FilterDrawerFooter } from "./FilterDrawerFooter";

import type { FilterConfig } from "../../types/table";

interface FilterDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  filterConfig: FilterConfig;
  onUsdSignalChange: (signal: FilterConfig["usdSignal"]) => void;
  onBtcSignalChange: (signal: FilterConfig["btcSignal"]) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;
}

// eslint-disable-next-line max-lines-per-function
export const FilterDrawer: React.FC<FilterDrawerProps> = ({
  isOpen,
  onClose,
  filterConfig,
  onUsdSignalChange,
  onBtcSignalChange,
  onClearFilters,
  hasActiveFilters,
  filteredCount,
  totalCount,
}) => {
  const { handleTouchStart } = useTouchGestures({ onSwipeDown: onClose });

  // Handle escape key and backdrop click
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      // Prevent body scroll - drawer uses natural height with no scrolling
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, [isOpen, onClose]);

  if (!isOpen) {return null;}

  return (
    <>
      <div
        className="filter-drawer-backdrop"
        onClick={onClose}
        onKeyDown={(e) => {
          if (e.key === "Escape") {
            onClose();
          }
        }}
        role="button"
        tabIndex={0}
        aria-label="Close filter drawer"
      />
      <div className="filter-drawer" onTouchStart={handleTouchStart}>
        <div className="filter-drawer-header">
          <h3>Filter Cryptocurrencies</h3>
          <button className="filter-drawer-close" onClick={onClose}>
            ✕
          </button>
        </div>

        <FilterDrawerContent
          filterConfig={filterConfig}
          onUsdSignalChange={onUsdSignalChange}
          onBtcSignalChange={onBtcSignalChange}
        />

        <FilterDrawerFooter
          filteredCount={filteredCount}
          totalCount={totalCount}
          hasActiveFilters={hasActiveFilters}
          onClearFilters={onClearFilters}
          onClose={onClose}
        />
      </div>
    </>
  );
};
