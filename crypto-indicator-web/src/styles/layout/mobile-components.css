/**
 * Mobile Components Styles
 * 
 * Styles for mobile-specific components including CryptoCard, FilterDrawer,
 * FilterToggle, and mobile layout containers.
 * 
 * Dependencies: variables.css, base.css
 */

/* Mobile Layout Styles */
.mobile-layout {
  position: relative;
  padding: 16px;
}

.crypto-cards-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 60px; /* Space for filter toggle */
}

.crypto-card {
  position: relative;
  padding: 16px;
  overflow: hidden;
  cursor: pointer;
  background: rgb(var(--bg-secondary-rgb), var(--glass-opacity-secondary));
  border: var(--glass-border-secondary);
  border-radius: 16px;
  box-shadow: var(--glass-shadow-secondary);
  backdrop-filter: var(--glass-blur-secondary);
  transition: all var(--micro-spring);
}

.crypto-card::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 2px;
  content: '';
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  opacity: 0;
  transition: opacity var(--micro-spring);
}

.crypto-card:hover::before,
.crypto-card:focus::before {
  opacity: 1;
}

.crypto-card:hover {
  box-shadow: var(--glass-shadow-primary);
  transform: translateY(-2px) scale(1.02);
}

.crypto-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.crypto-card-symbol {
  font-size: 18px;
  font-weight: 600;
}

.crypto-card-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-end;
}

.price-label {
  font-size: 10px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.price-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--accent-green);
}

.crypto-card-signals {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.signal-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.signal-label-card {
  font-size: 10px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.crypto-card-details {
  padding-top: 12px;
  margin-top: 12px;
  border-top: 1px solid var(--border-primary);
  animation: expand-details var(--micro-spring) ease-out;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 12px;
  color: var(--text-secondary);
}

.detail-value {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.crypto-card-expand-indicator {
  position: absolute;
  right: 12px;
  bottom: 8px;
}

.expand-icon {
  font-size: 10px;
  color: var(--text-secondary);
  transition: transform var(--micro-spring);
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Filter Toggle (Mobile FAB) */
.filter-toggle-container {
  position: fixed;
  top: 24px;
  left: 50%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  transform: translateX(-50%);
}

.filter-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  cursor: pointer;
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 50%;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
  transition: all var(--micro-spring);
}

.filter-toggle:hover {
  box-shadow: var(--glass-shadow-primary);
  transform: scale(1.1);
}

.filter-toggle.active {
  background: rgb(var(--accent-blue-rgb), 0.9);
}

.filter-icon {
  font-size: 20px;
}

.filter-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  background: var(--accent-red);
  border-radius: 50%;
}

.filter-status {
  padding: 4px 8px;
  font-size: 10px;
  color: var(--text-secondary);
  white-space: nowrap;
  background: rgb(var(--bg-tertiary-rgb), var(--glass-opacity-tertiary));
  border: var(--glass-border-tertiary);
  border-radius: 12px;
  box-shadow: var(--glass-shadow-tertiary);
  backdrop-filter: var(--glass-blur-tertiary);
}

/* Filter Drawer (Mobile Top Sheet) */
.filter-drawer-backdrop {
  position: fixed;
  inset: 0;
  z-index: 9998;
  background: rgb(0 0 0 / 0.5);
  backdrop-filter: blur(4px);
  animation: fade-in var(--micro-spring) ease-out;
}

.filter-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgb(var(--bg-primary-rgb), var(--glass-opacity-primary));
  border: var(--glass-border-primary);
  border-radius: 0 0 24px 24px;
  box-shadow: var(--glass-shadow-primary);
  backdrop-filter: var(--glass-blur-primary);
  animation: slide-down var(--spring-duration) var(--spring-easing);

  /* Force natural height with absolutely no scrolling */
  height: auto;
  min-height: auto;
  max-height: none;
  overflow: visible;
}

.filter-drawer * {
  overflow: visible;
  max-width: 100%;
  box-sizing: border-box;
}

.filter-drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 16px;
  border-bottom: 1px solid var(--border-primary);
}

.filter-drawer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.filter-drawer-close {
  padding: 4px;
  font-size: 18px;
  color: var(--text-secondary);
  cursor: pointer;
  background: none;
  border: none;
  border-radius: 50%;
  transition: all var(--micro-spring);
}

.filter-drawer-close:hover {
  color: var(--text-primary);
  background: var(--bg-elevated);
}

.filter-drawer-content {
  padding: 16px 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.filter-drawer-input,
.filter-drawer-select {
  width: 100%;
  padding: 12px 16px;
  font-size: 16px;
  color: var(--text-primary);
  background: var(--bg-elevated);
  border: var(--glass-border-secondary);
  border-radius: 12px;
  transition: all var(--micro-spring);
}

.filter-drawer-input:focus,
.filter-drawer-select:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgb(var(--accent-blue-rgb), 0.2);
}

.range-inputs-mobile {
  display: flex;
  gap: 12px;
}

.range-inputs-mobile .filter-drawer-input {
  flex: 1;
}

.filter-drawer-footer {
  padding: 16px 20px 24px;
  border-top: 1px solid var(--border-primary);
}

.filter-results-mobile {
  margin-bottom: 16px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.filter-drawer-actions {
  display: flex;
  gap: 12px;
}

.filter-drawer-clear,
.filter-drawer-apply {
  flex: 1;
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  border-radius: 12px;
  transition: all var(--micro-spring);
}

.filter-drawer-clear {
  color: var(--text-secondary);
  background: var(--bg-elevated);
}

.filter-drawer-clear:hover {
  color: white;
  background: var(--accent-red);
}

.filter-drawer-apply {
  color: white;
  background: var(--accent-blue);
}

.filter-drawer-apply:hover {
  background: var(--accent-purple);
  transform: translateY(-1px);
}

/* Mobile-specific FilterDrawer improvements */
@media (width <= 768px) {
  .filter-drawer-content {
    padding: 12px 16px;
  }

  .filter-section {
    margin-bottom: 16px;
  }

  .filter-drawer-header {
    padding: 16px 16px 12px;
  }

  .filter-drawer-footer {
    padding: 12px 16px 20px;
  }
}

@media (width <= 480px) {
  .filter-drawer-content {
    padding: 8px 12px;
  }

  .filter-drawer-header {
    padding: 12px 12px 8px;
  }

  .filter-drawer-footer {
    padding: 8px 12px 16px;
  }
}
