/**
 * Utility Styles
 * 
 * Global utility styles including scrollbars, selection, responsive breakpoints,
 * and other cross-cutting concerns.
 * 
 * Dependencies: variables.css, base.css
 */

/* Comfortable Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-teal);
  border-radius: 4px;
  box-shadow: var(--shadow-subtle);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
  box-shadow: var(--shadow-hover);
}

/* Selection styling */
::selection {
  color: var(--text-primary);
  background: rgb(148 226 213 / 0.3);
}

/* Mobile Breakpoints - Progressive Enhancement */
@media (width <= 480px) {
  .app-container {
    padding: 10px;
  }

  .header {
    padding: 12px 16px;
    margin-bottom: 20px;
  }

  .header-main {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .header-title {
    gap: 8px;
    font-size: 1.6rem;
  }

  .lightning-icon {
    font-size: 1.8rem;
  }

  .header-subtitle {
    font-size: 0.85rem;
  }

  .live-indicator {
    padding: 4px 8px;
    font-size: 0.65rem;
  }

  .live-dot {
    width: 4px;
    height: 4px;
  }

  /* Table Container - Enable horizontal scrolling with sticky first column */
  .table-container {
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Table - Minimum width to prevent cramping */
  .table {
    position: relative;
    min-width: 600px;
    table-layout: auto;
  }

  /* Sticky first column for mobile - solid backgrounds */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
  }

  .table th:first-child {
    z-index: 11;
    background: var(--bg-surface);
  }

  /* Mobile sticky column hover and alternating row backgrounds */
  .table tr:hover td:first-child {
    background: var(--bg-elevated);
  }

  .table tr:nth-child(even) td:first-child {
    background: var(--bg-secondary);
  }

  .table th, .table td {
    padding: 16px 12px;
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
  }

  .table th {
    font-size: 13px;
    font-weight: 600;
  }

  /* Signal Badge - WCAG AAA compliant touch targets */
  .signal-badge {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    min-height: 48px;
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 12px;
  }

  .signal-icon {
    min-width: 16px;
    font-size: 16px;
  }

  .signal-label-mobile {
    font-size: 12px;
    font-weight: 500;
  }

  /* Crypto Icon - Larger for better visibility */
  .crypto-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* Chart Modal - Full screen on small devices */
  .chart-modal-content {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    height: calc(100vh - 10px);
    max-height: calc(100vh - 10px);
    padding: 12px;
    margin: 5px;
    border-radius: 16px;
  }

  .chart-container {
    min-width: 280px;
    min-height: 200px;
    margin: 10px 0;
  }

  .chart-legend {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
    margin-top: 10px;
  }

  .legend-item {
    padding: 4px 8px;
    font-size: 12px;
  }

  /* Close button - Larger touch target */
  .chart-close-button {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    font-size: 18px;
  }

  /* Mobile-specific close button improvements */
  .close-button {
    /* Ensure it's always accessible */
    z-index: 1001;
    width: 48px;
    height: 48px;
    padding: 12px;
    font-size: 24px;
  }
}

@media (width <= 768px) and (width >= 481px) {
  .app-container {
    padding: 15px;
  }

  .header {
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  .header-main {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-title {
    gap: 10px;
    font-size: 1.8rem;
  }

  .lightning-icon {
    font-size: 2rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .live-indicator {
    padding: 4px 12px;
    font-size: 0.75rem;
  }

  .live-dot {
    width: 6px;
    height: 6px;
  }

  .stats-info {
    flex-direction: column;
    gap: 15px;
    padding: 16px;
    text-align: center;
  }

  /* Table - Better readability with sticky first column */
  .table-container {
    position: relative;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table {
    position: relative;
    min-width: 700px;
  }

  /* Sticky first column for tablets - solid backgrounds */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgb(0 0 0 / 0.1);
  }

  .table th:first-child {
    z-index: 11;
    background: var(--bg-surface);
  }

  /* Tablet sticky column hover and alternating row backgrounds */
  .table tr:hover td:first-child {
    background: var(--bg-elevated);
  }

  .table tr:nth-child(even) td:first-child {
    background: var(--bg-secondary);
  }

  .table th, .table td {
    padding: 14px 10px;
    font-size: 13px;
    line-height: 1.3;
  }

  /* Signal Badge - Improved touch targets */
  .signal-badge {
    gap: 6px;
    min-width: 44px;
    min-height: 44px;
    padding: 10px 14px;
    font-size: 12px;
    border-radius: 10px;
  }

  .signal-icon {
    min-width: 14px;
    font-size: 14px;
  }

  .signal-label {
    font-size: 11px;
  }

  .crypto-icon {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .chart-modal-content {
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    height: calc(85vh - 20px);
    max-height: calc(85vh - 20px);
    padding: 16px;
    margin: 10px;
    border-radius: 20px;
  }

  .chart-container {
    min-width: 320px;
    min-height: 240px;
    margin: 15px 0;
  }

  .chart-legend {
    gap: 10px;
    padding: 10px;
    margin-top: 12px;
  }

  .legend-item {
    font-size: 12px;
  }
}

/* Landscape Orientation Support */
@media (height <= 500px) and (orientation: landscape) {
  .app-container {
    padding: 8px;
  }

  .header {
    padding: 8px 16px;
    margin-bottom: 16px;
  }

  .header-main {
    flex-direction: row;
    gap: 16px;
    align-items: center;
  }

  .header-title {
    font-size: 1.4rem;
  }

  .chart-modal-content {
    height: calc(100vh - 10px);
    max-height: calc(100vh - 10px);
    padding: 8px;
  }

  .chart-container {
    min-height: 180px;
    margin: 8px 0;
  }

  .chart-legend {
    padding: 6px;
    margin-top: 8px;
  }
}

/* Touch-friendly improvements */
@media (pointer: coarse) {
  /* Increase touch targets for all interactive elements */
  button, .signal-badge, .chart-close-button {
    min-width: 48px;
    min-height: 48px;
  }

  /* Better spacing for touch interaction */
  .table th, .table td {
    padding: 16px 12px;
  }

  /* Larger text for better readability */
  .table {
    font-size: 14px;
  }
}

/* High DPI displays */
@media (resolution >= 2dppx), (resolution >= 192dpi) {
  .signal-badge, .crypto-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Very small screens - Alternative card layout */
@media (width <= 360px) {
  .table-container {
    overflow-x: visible;
  }

  .table {
    display: block;
    min-width: auto;
  }

  .table thead {
    display: none;
  }

  .table tbody {
    display: block;
  }

  .table tr {
    display: block;
    padding: 16px;
    margin-bottom: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
  }

  .table td {
    display: block;
    padding: 8px 0;
    font-size: 14px;
    border: none;
  }

  .table td::before {
    display: inline-block;
    width: 120px;
    font-weight: 600;
    color: var(--text-secondary);
    content: attr(data-label) ": ";
  }

  .signal-badge {
    margin-top: 8px;
  }

  .close-button {
    width: 52px;
    height: 52px;
    padding: 14px;
    font-size: 28px;
  }
}
