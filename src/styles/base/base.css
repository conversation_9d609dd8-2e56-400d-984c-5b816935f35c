/**
 * Base Global Styles
 * 
 * This file contains global styles that apply to the entire application,
 * including body, app container, and base animations.
 * 
 * Dependencies: variables.css
 */

body {
  height: 100vh;
  margin: 0;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--text-primary);
  background: var(--bg-primary);
}

.app-container {
  position: relative;
  box-sizing: border-box;
  height: 100vh;
  padding: 20px;
  overflow: hidden auto;
  background: var(--dark-bg-gradient);
}

.app-container::before {
  position: fixed;
  inset: 0;
  z-index: -1;
  pointer-events: none;
  content: '';
  background:
    radial-gradient(circle at 20% 80%, rgb(148 226 213 / 0.03) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgb(203 166 247 / 0.02) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgb(137 180 250 / 0.02) 0%, transparent 60%);
}

/* Base Animation Keyframes */
@keyframes live-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes expand-details {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-down {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }

  to {
    opacity: 1;
    backdrop-filter: var(--glass-blur);
  }
}

@keyframes modal-slide-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes spring-enter {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes spring-exit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }

  to {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
}

/* Spring Animation Classes */
.spring-enter {
  animation: spring-enter var(--spring-duration) var(--spring-easing);
}

.spring-exit {
  animation: spring-exit var(--micro-spring) ease-in;
}

/* Enhanced Hover Effects */
.interactive-element {
  cursor: pointer;
  transition: all var(--micro-spring);
}

.interactive-element:hover {
  box-shadow: var(--glass-shadow-secondary);
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}
